import { RiCloseLine } from "@remixicon/react";
import { useTranscription } from "hooks";
import { useEffect, useMemo, useRef, useState } from "react";
import { But<PERSON>, Modal } from "react-bootstrap";
import EditMode from "./EditMode";
import RecordMode from "./RecordMode";
import "./styles.scss";

interface DictateModalProps {
  show: boolean;
  onClose: () => void;
  onFinish: (text: string) => void;
}

export default function DictateModal({
  show,
  onClose,
  onFinish,
}: DictateModalProps) {
  const textareaRef = useRef<HTMLTextAreaElement | null>(null);
  const cursorPositionRef = useRef<number | null>(null);
  const {
    isRecording,
    transcriptions,
    startRecording,
    stopRecording,
    stopAndClearRecording,
    FINISH_DELAY_TIME,
  } = useTranscription({ textareaRef, type: "report_dictation" });

  const [view, setView] = useState<"record" | "edit">("record");
  const [text, setText] = useState<string>("");
  const [hasStarted, setHasStarted] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    setText(transcriptions || "");
    if (transcriptions && transcriptions.trim().length > 0) {
      setHasStarted(true);
    }
  }, [transcriptions]);

  useEffect(() => {
    if (!show) {
      setTimeout(() => {
        setView("record");
        setText("");
        setHasStarted(false);
        stopAndClearRecording();
      }, 0);
    }
  }, [show, stopAndClearRecording]);

  const handleToggleRecording = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
      setHasStarted(true);
    }
  };

  const handleFinishRecording = () => {
    setIsLoading(true);
    if (isRecording) {
      stopRecording();
    }
    setTimeout(() => {
      setIsLoading(false);
    }, FINISH_DELAY_TIME);
    setTimeout(() => setView("edit"), FINISH_DELAY_TIME);
  };

  const handleReset = () => {
    stopAndClearRecording();
    setText("");
    setHasStarted(false);
  };

  const handleFinalFinish = () => {
    const finalText = textareaRef.current?.value ?? text;
    onFinish((finalText || "").trim());
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const textarea = textareaRef.current;
    if (textarea) {
      cursorPositionRef.current = textarea.selectionStart;
    }
    setText(e.target.value);
  };

  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea && cursorPositionRef.current !== null) {
      textarea.selectionStart = cursorPositionRef.current;
      textarea.selectionEnd = cursorPositionRef.current;
    }
  }, [text]);

  const formattedDateTime = useMemo(() => {
    const now = new Date();
    const date = now.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    });
    const time = now.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
    return `${date} | ${time}`;
  }, [show]);

  const hasAnyText = (text ?? "").trim().length > 0;

  return (
    <Modal
      show={show}
      keyboard={false}
      centered
      className="dictate-modal"
      dialogClassName={`dictate-${view}`}
    >
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative p-4">
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={onClose}
        >
          <RiCloseLine size={40} color="#f9f9f9" />
        </Button>

        {view === "record" ? (
          <RecordMode
            config={{
              isRecording,
              handleToggleRecording,
              hasStarted,
              handleReset,
              handleFinishRecording,
              hasAnyText,
              isLoading,
            }}
          />
        ) : (
          <EditMode
            config={{
              formattedDateTime,
              textareaRef,
              text,
              handleTextChange,
              handleFinalFinish,
              hasAnyText,
            }}
          />
        )}
      </Modal.Body>
    </Modal>
  );
}
